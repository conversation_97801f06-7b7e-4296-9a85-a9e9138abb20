import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface FilterOptionsResponse {
  platforms: FilterOption[];
  merchants: FilterOption[];
  statuses: FilterOption[];
  networks: FilterOption[];
  transactionTypes: FilterOption[];
  error?: string;
}

// Helper function to get filter counts efficiently
async function getFilterCounts(column: string, limit?: number): Promise<FilterOption[]> {
  try {
    // Use Supabase's built-in query - much more efficient than the old batching approach
    const { data, error } = await supabaseAdmin
      .from('normalized_transactions')
      .select(column)
      .not(column, 'is', null);

    if (error) {
      console.error(`Error fetching ${column} data:`, error);
      return [];
    }

    // Count occurrences efficiently
    const counts = (data || []).reduce((acc: Record<string, number>, item: any) => {
      const value = item[column];
      if (value) {
        acc[value] = (acc[value] || 0) + 1;
      }
      return acc;
    }, {});

    return Object.entries(counts)
      .map(([value, count]) => ({
        value,
        label: formatLabel(column, value),
        count: count as number
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit || 100);
  } catch (error) {
    console.error(`Error getting filter counts for ${column}:`, error);
    return [];
  }
}

// Helper function to format labels
function formatLabel(column: string, value: string): string {
  switch (column) {
    case 'platform':
      return value === 'strackr' ? 'Strackr' : value.charAt(0).toUpperCase() + value.slice(1);
    case 'status':
    case 'transaction_type':
      return value.charAt(0).toUpperCase() + value.slice(1);
    default:
      return value;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Add cache control headers to ensure fresh data
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };

    // Use our efficient helper function to get all filter counts
    const [platforms, merchants, statuses, networks, transactionTypes] = await Promise.all([
      getFilterCounts('platform'),
      getFilterCounts('merchant_name', 50), // Limit merchants to top 50
      getFilterCounts('status'),
      getFilterCounts('network_name'),
      getFilterCounts('transaction_type')
    ]);

    const response: FilterOptionsResponse = {
      platforms,
      merchants,
      statuses,
      networks,
      transactionTypes
    };

    return NextResponse.json(response, { headers });

  } catch (error) {
    console.error('Filter options API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
