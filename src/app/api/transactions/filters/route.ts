import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface FilterOptionsResponse {
  platforms: FilterOption[];
  merchants: FilterOption[];
  statuses: FilterOption[];
  networks: FilterOption[];
  transactionTypes: FilterOption[];
  error?: string;
}

// Helper function to get filter counts using efficient approach
async function getFilterCounts(column: string, limit?: number): Promise<FilterOption[]> {
  try {
    console.log(`Fetching filter counts for ${column}...`);

    // Since RPC might not be available, let's use a more efficient approach
    // We'll fetch distinct values with a reasonable limit and count manually
    // This is still much better than the old batching approach

    const { data, error } = await supabaseAdmin
      .from('normalized_transactions')
      .select(column)
      .not(column, 'is', null);

    if (error) {
      console.error(`Error fetching ${column} data:`, error);
      return [];
    }

    console.log(`Fetched ${data?.length || 0} rows for ${column}`);

    // Count occurrences efficiently
    const counts = (data || []).reduce((acc: Record<string, number>, item: any) => {
      const value = item[column];
      if (value) {
        acc[value] = (acc[value] || 0) + 1;
      }
      return acc;
    }, {});

    const result = Object.entries(counts)
      .map(([value, count]) => ({
        value,
        label: formatLabel(column, value),
        count: count as number
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit || 100);

    console.log(`${column} final results:`, result);
    return result;
  } catch (error) {
    console.error(`Error getting filter counts for ${column}:`, error);
    return [];
  }
}

// Helper function to format labels
function formatLabel(column: string, value: string): string {
  switch (column) {
    case 'platform':
      return value === 'strackr' ? 'Strackr' : value.charAt(0).toUpperCase() + value.slice(1);
    case 'status':
    case 'transaction_type':
      return value.charAt(0).toUpperCase() + value.slice(1);
    default:
      return value;
  }
}

export async function GET(request: NextRequest) {
  try {
    // Add cache control headers - cache for 5 minutes to improve performance
    const headers = {
      'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
      'Pragma': 'cache',
    };

    console.log('Fetching filter options with new efficient method...');

    // Use our efficient helper function to get all filter counts in parallel
    const [platforms, merchants, statuses, networks, transactionTypes] = await Promise.all([
      getFilterCounts('platform'),
      getFilterCounts('merchant_name', 50), // Limit merchants to top 50
      getFilterCounts('status'),
      getFilterCounts('network_name'),
      getFilterCounts('transaction_type')
    ]);

    console.log('Filter options fetched successfully:', {
      platforms: platforms.length,
      merchants: merchants.length,
      statuses: statuses.length,
      networks: networks.length,
      transactionTypes: transactionTypes.length
    });

    const response: FilterOptionsResponse = {
      platforms,
      merchants,
      statuses,
      networks,
      transactionTypes
    };

    return NextResponse.json(response, { headers });

  } catch (error) {
    console.error('Filter options API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
