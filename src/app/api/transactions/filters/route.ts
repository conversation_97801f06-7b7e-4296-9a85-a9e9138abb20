import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

interface FilterOption {
  value: string;
  label: string;
  count: number;
}

interface FilterOptionsResponse {
  platforms: FilterOption[];
  merchants: FilterOption[];
  statuses: FilterOption[];
  networks: FilterOption[];
  transactionTypes: FilterOption[];
  error?: string;
}

export async function GET(request: NextRequest) {
  try {
    // Add cache control headers to ensure fresh data
    const headers = {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };
    // Validate database connection first
    const { data: healthCheck, error: healthError } = await supabaseAdmin
      .from('normalized_transactions')
      .select('id')
      .limit(1);

    if (healthError) {
      console.error('Database connection error:', healthError);
      return NextResponse.json(
        { error: 'Database connection failed', details: healthError.message },
        { status: 503 }
      );
    }

    // Get distinct platforms with counts
    // Note: Supabase has a default limit of 1000 rows, so we need to fetch in batches
    let allPlatformData: any[] = [];
    let hasMore = true;
    let offset = 0;
    const batchSize = 1000;

    while (hasMore) {
      const { data: batchData, error: batchError } = await supabaseAdmin
        .from('normalized_transactions')
        .select('platform')
        .not('platform', 'is', null)
        .range(offset, offset + batchSize - 1);

      if (batchError) {
        console.error('Platform query error:', batchError);
        return NextResponse.json(
          { error: 'Failed to fetch platform options', details: batchError.message },
          { status: 500 }
        );
      }

      if (batchData && batchData.length > 0) {
        allPlatformData = allPlatformData.concat(batchData);
        offset += batchSize;
        hasMore = batchData.length === batchSize;
      } else {
        hasMore = false;
      }

      // Safety check
      if (offset > 10000) break;
    }

    const platformData = allPlatformData;

    // Get distinct merchants with counts (using the same batch approach)
    let allMerchantData: any[] = [];
    hasMore = true;
    offset = 0;

    while (hasMore) {
      const { data: batchData, error: batchError } = await supabaseAdmin
        .from('normalized_transactions')
        .select('merchant_name')
        .not('merchant_name', 'is', null)
        .range(offset, offset + batchSize - 1);

      if (batchError) {
        console.error('Merchant query error:', batchError);
        return NextResponse.json(
          { error: 'Failed to fetch merchant options', details: batchError.message },
          { status: 500 }
        );
      }

      if (batchData && batchData.length > 0) {
        allMerchantData = allMerchantData.concat(batchData);
        offset += batchSize;
        hasMore = batchData.length === batchSize;
      } else {
        hasMore = false;
      }

      if (offset > 10000) break;
    }

    const merchantData = allMerchantData;

    // Get distinct statuses with counts (using the same batch approach)
    let allStatusData: any[] = [];
    hasMore = true;
    offset = 0;

    while (hasMore) {
      const { data: batchData, error: batchError } = await supabaseAdmin
        .from('normalized_transactions')
        .select('status')
        .not('status', 'is', null)
        .range(offset, offset + batchSize - 1);

      if (batchError) {
        console.error('Status query error:', batchError);
        return NextResponse.json(
          { error: 'Failed to fetch status options', details: batchError.message },
          { status: 500 }
        );
      }

      if (batchData && batchData.length > 0) {
        allStatusData = allStatusData.concat(batchData);
        offset += batchSize;
        hasMore = batchData.length === batchSize;
      } else {
        hasMore = false;
      }

      if (offset > 10000) break;
    }

    const statusData = allStatusData;

    // Get distinct networks with counts (using the same batch approach)
    let allNetworkData: any[] = [];
    hasMore = true;
    offset = 0;

    while (hasMore) {
      const { data: batchData, error: batchError } = await supabaseAdmin
        .from('normalized_transactions')
        .select('network_name')
        .not('network_name', 'is', null)
        .range(offset, offset + batchSize - 1);

      if (batchError) {
        console.error('Network query error:', batchError);
        return NextResponse.json(
          { error: 'Failed to fetch network options', details: batchError.message },
          { status: 500 }
        );
      }

      if (batchData && batchData.length > 0) {
        allNetworkData = allNetworkData.concat(batchData);
        offset += batchSize;
        hasMore = batchData.length === batchSize;
      } else {
        hasMore = false;
      }

      if (offset > 10000) break;
    }

    const networkData = allNetworkData;

    // Get distinct transaction types with counts using a simple query
    const { data: transactionTypeData, error: transactionTypeError } = await supabaseAdmin
      .from('normalized_transactions')
      .select('transaction_type')
      .not('transaction_type', 'is', null);

    if (transactionTypeError) {
      console.error('Transaction type query error:', transactionTypeError);
      return NextResponse.json(
        { error: 'Failed to fetch transaction type options', details: transactionTypeError.message },
        { status: 500 }
      );
    }

    // Process platforms
    const platformCounts = platformData.reduce((acc: Record<string, number>, item) => {
      const platform = item.platform;
      acc[platform] = (acc[platform] || 0) + 1;
      return acc;
    }, {});

    const platforms: FilterOption[] = Object.entries(platformCounts)
      .map(([value, count]) => ({
        value,
        label: value === 'strackr' ? 'Strackr' : value.charAt(0).toUpperCase() + value.slice(1),
        count
      }))
      .sort((a, b) => b.count - a.count);

    // Process merchants (limit to top 50 by transaction count)
    const merchantCounts = merchantData.reduce((acc: Record<string, number>, item) => {
      const merchant = item.merchant_name;
      acc[merchant] = (acc[merchant] || 0) + 1;
      return acc;
    }, {});

    const merchants: FilterOption[] = Object.entries(merchantCounts)
      .map(([value, count]) => ({
        value,
        label: value,
        count
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 50); // Limit to top 50 merchants

    // Process statuses
    const statusCounts = statusData.reduce((acc: Record<string, number>, item) => {
      const status = item.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const statuses: FilterOption[] = Object.entries(statusCounts)
      .map(([value, count]) => ({
        value,
        label: value.charAt(0).toUpperCase() + value.slice(1),
        count
      }))
      .sort((a, b) => b.count - a.count);

    // Process networks
    const networkCounts = networkData.reduce((acc: Record<string, number>, item) => {
      const network = item.network_name;
      acc[network] = (acc[network] || 0) + 1;
      return acc;
    }, {});

    const networks: FilterOption[] = Object.entries(networkCounts)
      .map(([value, count]) => ({
        value,
        label: value,
        count
      }))
      .sort((a, b) => b.count - a.count);

    // Process transaction types
    const transactionTypeCounts = (transactionTypeData || []).reduce((acc: Record<string, number>, item: any) => {
      const transactionType = item.transaction_type;
      if (transactionType) {
        acc[transactionType] = (acc[transactionType] || 0) + 1;
      }
      return acc;
    }, {});

    const transactionTypes: FilterOption[] = Object.entries(transactionTypeCounts)
      .map(([value, count]) => ({
        value,
        label: value.charAt(0).toUpperCase() + value.slice(1),
        count: count as number
      }))
      .sort((a, b) => b.count - a.count);

    const response: FilterOptionsResponse = {
      platforms,
      merchants,
      statuses,
      networks,
      transactionTypes
    };

    return NextResponse.json(response, { headers });

  } catch (error) {
    console.error('Filter options API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
